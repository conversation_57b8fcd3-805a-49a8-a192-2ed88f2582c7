"""
BPMN Generator Module

This module handles the generation of BPMN diagrams using OpenAI API for both analysis
and visualization, based on the structured data from the RCM analysis.
"""

import os
import json
import base64
from io import BytesIO
from openai import OpenAI
import google.generativeai as genai
from PIL import Image, ImageDraw, ImageFont
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure OpenAI API
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4o")
OPENAI_TEMPERATURE = float(os.getenv("OPENAI_TEMPERATURE", 0.2))
OPENAI_MAX_TOKENS = int(os.getenv("OPENAI_MAX_TOKENS", 8192))

# Configure Google Gemini API
genai.configure(api_key=os.getenv("GOOGLE_API_KEY"))
GEMINI_MODEL = os.getenv("LLM_MODEL", "gemini-2.0-flash")
GEMINI_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", 0.4))
GEMINI_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", 8192))

def generate_bpmn_json_structure(subprocess_data):
    """
    Generate a JSON structure for BPMN diagram using OpenAI API.

    Args:
        subprocess_data: Structured data for the subprocess

    Returns:
        dict: JSON structure for BPMN diagram
    """
    # Parse the subprocess data if it's a string
    if isinstance(subprocess_data, str):
        try:
            subprocess_data = json.loads(subprocess_data)
        except json.JSONDecodeError:
            # If it's not valid JSON, we'll use it as is
            pass

    # Extract key information from subprocess_data
    process_name = subprocess_data.get('subprocess_name', 'Business Process')

    # Extract lanes and tasks information
    lanes_info = []
    for lane in subprocess_data.get('lanes', []):
        lane_name = lane.get('lane_name', '')
        tasks = []
        for task in lane.get('tasks', []):
            # Get task title and description
            task_name = task.get('task_name', '')
            task_description = task.get('task_description', '')

            # Create task info with description as the primary content
            # This ensures the description will be displayed inside the task element
            task_info = {
                'name': task_description,  # Use description as the name (will be displayed in task element)
                'description': task_description,  # Also use description in the documentation element
                'original_name': task_name,  # Keep original name for reference
                'original_description': task_description,  # Keep original description for reference
                'systems': [s.get('system_name', '') for s in task.get('systems', []) if s.get('system_name')],
                'risks': [r.get('risk_id', '') + ': ' + r.get('risk_description', '') for r in task.get('risks', []) if r.get('risk_id')],
                'controls': [c.get('control_id', '') + ': ' + c.get('control_description', '') for c in task.get('controls', []) if c.get('control_id')]
            }
            tasks.append(task_info)

        lanes_info.append({
            'name': lane_name,
            'tasks': tasks
        })

    # Create a simplified JSON representation
    simplified_data = {
        'process_name': process_name,
        'lanes': lanes_info,
        'systems_legend': subprocess_data.get('systems_legend', [])
    }

    # Create the prompt for OpenAI
    prompt = f"""
You are a GRC partner at a top consulting firm with excellence in SOX compliance and business process documentation.

YOUR TASK: Create a structured JSON representation for a BPMN 2.0 diagram showing this business process:

Process name: {process_name}

Process data: {json.dumps(simplified_data, indent=2)}

The JSON structure should include all elements needed for a BPMN diagram:
- Pool representing the subprocess
- Lanes for each department/role with the following STRICT constraints:
  * Each pool MUST contain a MINIMUM of 2 lanes to properly represent process participants
  * Each pool MUST contain a MAXIMUM of 3 lanes per pool
  * If more than 3 lanes are identified in the RCM data, combine related roles/departments to stay within the 3-lane limit
  * Do NOT assume start/end events as separate lanes
- Tasks with DETAILED DESCRIPTIONS inside the task boxes (not just task titles)
  * CRITICAL: Display task descriptions (not task titles) as the primary content inside task elements
  * Extract meaningful descriptions from the RCM data for each task with at least 30 words
  * Include specific actions, responsibilities, and context in the descriptions
  * Ensure descriptions are concise enough to fit within task boxes while conveying essential information
  * Use the task description as the "name" field in the JSON structure (this will be displayed in the task element)
  * Dynamically size task elements based on description length to ensure full visibility
- Start and end events
- Gateways for decision points
- Sequence flows connecting all elements
- Systems as standard BPMN data store symbols (cylindrical database icons):
  * ONLY include systems where they are explicitly mentioned for specific tasks
  * Connect each system to its relevant task with a direct connecting line/arrow below the task
  * Position systems directly below their associated tasks
- Risks as triangle-shaped elements with only the risk ID number inside (no description)
- Controls as diamond-shaped elements with only the control ID number inside (no description)

Return ONLY the JSON structure in the following format:
{{
  "process_name": "Name of the process",
  "pool": {{
    "id": "pool_1",
    "name": "Process Name"
  }},
  "lanes": [
    {{
      "id": "lane_1",
      "name": "Department/Role Name",
      "elements": [
        {{
          "id": "start_event_1",
          "type": "startEvent",
          "name": "Start",
          "x": 100,
          "y": 100
        }},
        {{
          "id": "task_1",
          "type": "task",
          "name": "Task Name",
          "description": "Detailed task description",
          "x": 200,
          "y": 100,
          "risks": [
            {{
              "id": "risk_1",
              "shape": "triangle"
            }}
          ],
          "controls": [
            {{
              "id": "control_1",
              "shape": "diamond"
            }}
          ],
          "systems": [
            {{
              "id": "system_1",
              "name": "System name"
            }}
          ]
        }},
        {{
          "id": "gateway_1",
          "type": "exclusiveGateway",
          "name": "Decision",
          "x": 300,
          "y": 100
        }},
        {{
          "id": "end_event_1",
          "type": "endEvent",
          "name": "End",
          "x": 400,
          "y": 100
        }}
      ]
    }}
  ],
  "flows": [
    {{
      "id": "flow_1",
      "source": "start_event_1",
      "target": "task_1"
    }},
    {{
      "id": "flow_2",
      "source": "task_1",
      "target": "gateway_1"
    }},
    {{
      "id": "flow_3",
      "source": "gateway_1",
      "target": "end_event_1"
    }}
  ],
  "systems_legend": [
    {{
      "id": "system_1",
      "name": "System Name",
      "description": "System Description"
    }}
  ]
}}

IMPORTANT REQUIREMENTS:
1. Include ALL relevant departments/roles as lanes - there is NO MAXIMUM LIMIT on the number of lanes
2. Each pool MUST contain a MINIMUM of 2 lanes to properly represent process participants
3. ONLY associate systems with SPECIFIC tasks where they are EXPLICITLY mentioned in the data
4. Do NOT automatically add system references to every task
5. Ensure the JSON structure follows proper BPMN 2.0 standards and includes all necessary elements for a complete diagram
6. CRITICAL LAYOUT CONSTRAINTS:
   - Each pool MUST contain EXACTLY ONE start event and EXACTLY ONE end event - no more, no less
   - Position the start event in the FIRST lane at x=100, y=100.
   - Ensure NO empty lanes (each lane must contain at least one task)
   - CRITICAL LAYOUT AND POSITIONING REQUIREMENTS FOR BPMN ELEMENTS:

   1. **Dynamic Horizontal Positioning (X-coordinates)**:
      - Start event: x=100, y=100 (positioned in first lane)
      - First task in first lane: x=200, y=100
      - Subsequent tasks in same lane: increment x by 200px (x=400, x=600, x=800, etc.)
      - Tasks in different lanes: maintain horizontal progression OR position directly below previous lane's task
      - End event: position at the rightmost x-coordinate after all tasks are placed

   2. **Dynamic Vertical Positioning (Y-coordinates) - Enhanced:**
      - Lane 1: All elements positioned at y=175 (center of lane with bounds y=50 to y=300)
      - Lane 2: All elements positioned at y=425 (center of lane with bounds y=300 to y=550)
      - Lane 3: All elements positioned at y=675 (center of lane with bounds y=550 to y=800)
      - Continue this pattern for additional lanes, calculating the center y-coordinate of each lane's vertical bounds

      **Lane Center Calculation Formula:**
      - Lane center y-coordinate = (lane_top_y + lane_bottom_y) / 2
      - Ensure all tasks, events, and other elements within a lane use the same center y-coordinate
      - This creates perfect horizontal alignment of all elements within each lane
      - Maintain the 200px minimum spacing between lane boundaries while using center positioning

   3. **Cross-Lane Task Positioning Options**:
      - Option A: Continue horizontal progression (if task moves to next lane, use next available x-coordinate like x=600, y=425)
      - Option B: Vertical alignment (position task directly below the last task from previous lane, e.g., x=400, y=425)
      - Always use the target lane's center y-coordinate (y=175 for Lane 1, y=425 for Lane 2, y=675 for Lane 3)

   4. **Sequence Flow Connection Requirements**:
      - Ensure all connecting arrows (sequence flows) use proper waypoints that create clean orthogonal paths
      - For cross-lane connections: flow from bottom center of source task to top center of target task
      - Maintain consistent spacing and avoid overlapping flows
      - Use multiple waypoints for complex routing to ensure visual clarity

   5. **Visual Layout Goals**:
      - Create a logical left-to-right flow progression
      - Ensure no overlapping elements or awkward positioning
      - Maintain consistent spacing between all elements (minimum 50px)
      - Position end event as the rightmost element in the final lane
      - Ensure all elements are properly aligned within their respective lanes

   6. **CRITICAL BPMN LAYOUT AND ELEMENT SIZING REQUIREMENTS:**

      **Dynamic Lane Sizing and Element Containment:**
      - Ensure all BPMN elements (start events, tasks, gateways, end events) are completely contained within their respective lane boundaries
      - No element should extend outside or overlap with lane borders
      - Lanes should dynamically expand their width and height based on the content they contain
      - Calculate minimum lane dimensions based on the number and size of elements within each lane
      - Add appropriate padding (minimum 20px) between lane borders and contained elements

      **Task Element Sizing and Content Display:**
      - Dynamically size task elements based on the length of their description text
      - Display task descriptions (not task titles) inside the task elements as the primary content
      - Ensure task descriptions are fully visible and readable within the task boundaries
      - Implement text wrapping and appropriate font sizing to fit descriptions within task elements
      - Minimum task size: 120px width × 80px height
      - Maximum task width: 300px (increase height for longer descriptions)

      **Element Overlap Prevention:**
      - Implement collision detection to prevent any BPMN elements from overlapping
      - Maintain minimum spacing of 50px between adjacent elements
      - Ensure sequence flows do not overlap with other elements
      - Adjust element positioning automatically if overlaps are detected

      **Layout Analysis and Strategy Optimization:**
      - Analyze the current xy positioning strategy for visual clarity and effectiveness
      - If the current lane center positioning creates layout issues, implement alternative positioning strategies
      - Consider horizontal flow optimization to prevent overcrowding
      - Implement intelligent spacing algorithms that adapt to content density

      **Implementation Priority:**
      - Fix task description display issue (descriptions not showing instead of titles)
      - Implement dynamic lane expansion based on content
      - Add element containment validation
      - Optimize positioning strategy for clean visuals
      - Ensure all elements are properly contained within lane boundaries
      - Generate BPMN diagrams with properly sized lanes that contain all elements
      - Display task descriptions clearly within appropriately sized task elements
      - Maintain clean visual layout without any overlapping or out-of-bounds elements

   - Ensure clear flow from start to end across all relevant lanes.
   - ALL x and y coordinates MUST be NUMERIC.  Do not include units (e.g., "px").

"""

    try:
        # Configure model for OpenAI
        print(f"Using OpenAI model: {OPENAI_MODEL}")
        print("Sending request to OpenAI to generate BPMN JSON structure...")

        # Request JSON structure from OpenAI
        response = client.chat.completions.create(
            model=OPENAI_MODEL,
            temperature=OPENAI_TEMPERATURE,
            max_tokens=OPENAI_MAX_TOKENS,
            messages=[
                {"role": "system", "content": "You are a GRC expert specializing in business process documentation and BPMN 2.0 diagrams."},
                {"role": "user", "content": prompt}
            ]
        )

        # Extract the JSON structure from the OpenAI response
        if response.choices and len(response.choices) > 0:
            json_structure_text = response.choices[0].message.content
            print(f"Received BPMN JSON structure from OpenAI")

            # Try to parse the JSON structure
            try:
                # Extract JSON object from the response
                import re
                json_match = re.search(r'\{.*\}', json_structure_text, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                    json_structure = json.loads(json_str)
                    return json_structure
                else:
                    # If no JSON object found, try to parse the entire response
                    json_structure = json.loads(json_structure_text)
                    return json_structure
            except Exception as parse_error:
                print(f"Error parsing JSON structure: {str(parse_error)}")
                raise Exception(f"Failed to parse JSON structure: {str(parse_error)}")
        else:
            print("No content in OpenAI response")
            raise Exception("OpenAI did not return any content")

    except Exception as e:
        print(f"Error with OpenAI API: {str(e)}")
        raise Exception(f"OpenAI API failed: {str(e)}")

def generate_bpmn_xml(bpmn_json_structure):
    """
    Generate a BPMN 2.0 XML using OpenAI API.

    Args:
        bpmn_json_structure: JSON structure for BPMN diagram

    Returns:
        str: BPMN 2.0 XML content
    """
    try:
        # Configure OpenAI model
        print(f"Using OpenAI model: {OPENAI_MODEL}")

        # Create the prompt for OpenAI
        prompt = f"""
You are a BPMN expert specializing in generating valid BPMN 2.0 XML diagrams.

Carefully generate BPMN 2.0 XML for the following process:

{json.dumps(bpmn_json_structure, indent=2)}

Pay close attention to these requirements:

CRITICAL XML STRUCTURE REQUIREMENTS (EXTREMELY IMPORTANT):
1. Create a complete and valid BPMN 2.0 XML document following the official BPMN 2.0 specification.
2. STRICTLY ADHERE to the official BPMN 2.0 XML schema (http://www.omg.org/spec/BPMN/********/MODEL).
3. Ensure proper XML structure with correctly nested tags - every opening tag MUST have a matching closing tag.
4. All XML attributes MUST be properly quoted with double quotes.
5. Avoid self-closing tags for complex elements - use full opening and closing tags.
6. Ensure proper indentation and formatting for readability.
7. Include all necessary XML namespaces and schema references in the root element:
   - xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL"
   - xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI"
   - xmlns:dc="http://www.omg.org/spec/DD/********/DC"
   - xmlns:di="http://www.omg.org/spec/DD/********/DI"
   - xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
   - xsi:schemaLocation="http://www.omg.org/spec/BPMN/********/MODEL http://www.omg.org/spec/BPMN/2.0/20100501/BPMN20.xsd"
8. Follow the EXACT element order as specified in the BPMN 2.0 XML schema.
9. Include ALL required attributes for each element type as defined in the schema.
10. Use valid attribute values that conform to the schema's data type requirements.
11. Maintain proper parent-child relationships between elements as defined in the schema.
12. Ensure all elements are properly closed in the correct order (no overlapping tags).
13. Do not include any custom or non-standard XML elements or attributes.
14. Validate the XML structure before returning it to ensure it is well-formed and schema-valid.
15. Ensure all IDs are unique and follow BPMN naming conventions.
16. Make sure all referenced elements exist in the XML.
17. Ensure all BPMNShape and BPMNEdge elements have a bpmnElement attribute referencing an existing element.
18. Ensure all sequence flows have both sourceRef and targetRef attributes pointing to existing elements.
19. All lanes must be contained within a laneSet element.
20. If using a participant, ensure it has a proper processRef attribute.

BPMN DIAGRAM CONTENT REQUIREMENTS:
1. Create a process with the correct ID and name.
2. Include lanes for each department/role with the following STRICT constraints:
   - Each pool MUST contain a MINIMUM of 2 lanes to properly represent process participants.
   - Each pool MUST contain a MAXIMUM of 3 lanes per pool.
   - If the JSON structure has more than 3 lanes, combine related roles/departments to stay within the 3-lane limit.
   - If the JSON structure has only 1 lane, create at least one additional lane.
   - Lanes MUST ONLY represent process owners or departments (not systems or other entities).
   - Each lane MUST contain at least one task (NO EMPTY LANES allowed).
   - Do NOT assume start/end events as separate lanes.
3. Create tasks with DETAILED DESCRIPTIONS inside the task boxes:
   - Use the task's "name" field from the JSON structure as the primary content (this contains the task description).
   - Use the EXACT SAME TEXT for both the task name (bpmn:task name attribute) AND in the bpmn:documentation element.
   - DO NOT use the "description" field as a separate element - the task description is already in the "name" field.
   - Ensure the complete task description text is visible and properly formatted within each task element.
   - Dynamically size each task element based on the length of its description text.
   - Make task elements wider and/or taller as needed to accommodate longer descriptions.
   - Use appropriate text size and wrapping to make descriptions readable.
4. Add EXACTLY ONE start event and EXACTLY ONE end event per pool with the following STRICT constraints:
   - Each pool MUST contain EXACTLY ONE start event and EXACTLY ONE end event - no more, no less.
   - Position the start event in the FIRST lane.
   - Position the end event in the LAST lane.
   - Ensure clear flow from start to end across all relevant lanes.
   - CRITICAL LAYOUT AND POSITIONING REQUIREMENTS FOR BPMN ELEMENTS:

   1. **Dynamic Horizontal Positioning (X-coordinates)**:
      - Start event: x=100, y=100 (positioned in first lane)
      - First task in first lane: x=200, y=100
      - Subsequent tasks in same lane: increment x by 200px (x=400, x=600, x=800, etc.)
      - Tasks in different lanes: maintain horizontal progression OR position directly below previous lane's task
      - End event: position at the rightmost x-coordinate after all tasks are placed

   2. **Dynamic Vertical Positioning (Y-coordinates) - Enhanced:**
      - Lane 1: All elements positioned at y=175 (center of lane with bounds y=50 to y=300)
      - Lane 2: All elements positioned at y=425 (center of lane with bounds y=300 to y=550)
      - Lane 3: All elements positioned at y=675 (center of lane with bounds y=550 to y=800)
      - Continue this pattern for additional lanes, calculating the center y-coordinate of each lane's vertical bounds

      **Lane Center Calculation Formula:**
      - Lane center y-coordinate = (lane_top_y + lane_bottom_y) / 2
      - Ensure all tasks, events, and other elements within a lane use the same center y-coordinate
      - This creates perfect horizontal alignment of all elements within each lane
      - Maintain the 200px minimum spacing between lane boundaries while using center positioning

   3. **Cross-Lane Task Positioning Options**:
      - Option A: Continue horizontal progression (if task moves to next lane, use next available x-coordinate like x=600, y=425)
      - Option B: Vertical alignment (position task directly below the last task from previous lane, e.g., x=400, y=425)
      - Always use the target lane's center y-coordinate (y=175 for Lane 1, y=425 for Lane 2, y=675 for Lane 3)

   4. **Sequence Flow Connection Requirements**:
      - Ensure all connecting arrows (sequence flows) use proper waypoints that create clean orthogonal paths
      - For cross-lane connections: flow from bottom center of source task to top center of target task
      - Maintain consistent spacing and avoid overlapping flows
      - Use multiple waypoints for complex routing to ensure visual clarity

   5. **Visual Layout Goals**:
      - Create a logical left-to-right flow progression
      - Ensure no overlapping elements or awkward positioning
      - Maintain consistent spacing between all elements (minimum 50px)
      - Position end event as the rightmost element in the final lane
      - Ensure all elements are properly aligned within their respective lanes

   6. **CRITICAL BPMN LAYOUT AND ELEMENT SIZING REQUIREMENTS:**

      **Dynamic Lane Sizing and Element Containment:**
      - Ensure all BPMN elements (start events, tasks, gateways, end events) are completely contained within their respective lane boundaries
      - No element should extend outside or overlap with lane borders
      - Lanes should dynamically expand their width and height based on the content they contain
      - Calculate minimum lane dimensions based on the number and size of elements within each lane
      - Add appropriate padding (minimum 20px) between lane borders and contained elements

      **Task Element Sizing and Content Display:**
      - Dynamically size task elements based on the length of their description text
      - Display task descriptions (not task titles) inside the task elements as the primary content
      - Ensure task descriptions are fully visible and readable within the task boundaries
      - Implement text wrapping and appropriate font sizing to fit descriptions within task elements
      - Minimum task size: 120px width × 80px height
      - Maximum task width: 300px (increase height for longer descriptions)

      **Element Overlap Prevention:**
      - Implement collision detection to prevent any BPMN elements from overlapping
      - Maintain minimum spacing of 50px between adjacent elements
      - Ensure sequence flows do not overlap with other elements
      - Adjust element positioning automatically if overlaps are detected

      **Layout Analysis and Strategy Optimization:**
      - Analyze the current xy positioning strategy for visual clarity and effectiveness
      - If the current lane center positioning creates layout issues, implement alternative positioning strategies
      - Consider horizontal flow optimization to prevent overcrowding
      - Implement intelligent spacing algorithms that adapt to content density

5. Connect all elements with sequence flows.
6. Include data stores for systems.
7. Represent risks as TRIANGLE-SHAPED elements with ONLY the risk ID number inside (no description).
8. Represent controls as DIAMOND-SHAPED elements with ONLY the control ID number inside (no description).
9. Position risk and control elements DIRECTLY ADJACENT to their associated tasks WITHOUT connecting lines.
10. Include appropriate documentation elements for descriptions.
11. Ensure all BPMNShape elements have proper dc:Bounds with x, y, width, and height attributes.
12. Ensure all BPMNEdge elements have at least two di:waypoint elements with x and y coordinates.
13. Make sure there are no NaN values in any coordinates.
14. For custom elements like risks, controls, and systems, use standard BPMN elements:
    - Each risk MUST be paired with exactly ONE control (1:1 relationship).
    - The risk ID and its corresponding control ID must always use the same number (R1 pairs with C1, R2 with C2, etc.).
    - For risks, use bpmn:task elements with custom rendering as RED TRIANGLES with ONLY the risk ID inside.
    - For controls, use bpmn:task elements with custom rendering as GREEN DIAMONDS with ONLY the control ID inside.
    - Position risk triangles at the RIGHT TOP CORNER of their associated tasks.
    - Position control diamonds at the RIGHT BOTTOM CORNER of their associated tasks.
    - For systems:
      * Use bpmn:dataStoreReference elements (standard BPMN data store symbols).
      * ONLY include systems where they are explicitly mentioned for specific tasks.
      * Connect each system to its relevant task with a direct dataInputAssociation arrow.
      * Position systems directly below their associated tasks.
    - Do NOT use connecting lines between risks/controls and their tasks.
15. ONLY use standard BPMN elements (do not create custom elements).
16. All elements must be defined BEFORE they are referenced in sequence flows.
17. Follow bpmn-js library best practices (https://github.com/bpmn-io/bpmn-js) for optimal rendering.

LAYOUT REQUIREMENTS (EXTREMELY IMPORTANT):
1. Use a landscape orientation for all diagrams with flexible dimensions.
   - Do NOT constrain to exact A4 dimensions.
   - Use a width-to-height ratio of approximately 1.4:1 (landscape orientation).
   - Allow the diagram to expand as needed based on content.
2. Divide the diagram into EQUAL lanes:
   - All lanes MUST have the SAME width spanning the entire diagram width (minus margins).
   - Lanes MUST be perfectly aligned with no gaps or overlaps between them.
   - Distribute lane heights equally based on the number of lanes.
3. Use the following guidelines for lane dimensions:
   - Minimum lane height: 200px.
   - Minimum lane width: 800px.
   - Allow lanes to expand horizontally and vertically as needed.
4. Dynamically size task elements based on their content:
   - Base task element width and height on the length of the description text.
   - For tasks with longer descriptions, increase the width and/or height proportionally.
   - Minimum task width: 120px.
   - Minimum task height: 80px.
   - Maximum task width: 300px (use increased height for very long descriptions).
   - Ensure all text is visible and readable within the task element.
   - Left margin for lane names: approximately 150px.
5. Increase spacing between elements to prevent overcrowding and ensure the process flow is clearly visible.
6. Ensure all text labels, task descriptions, and other elements are properly sized and positioned for readability.
7. Maintain appropriate margins around the entire diagram (at least 20px on all sides).
8. Apply consistent spacing between connected elements to improve visual clarity (at least 50px between tasks).
9. Position the start event at the left side of the diagram and the end event at the right side.
10. Ensure the diagram has a professional, balanced appearance with elements evenly distributed.
11. Position start events at the same vertical level (y-coordinate) as their first connected task.
12. Position end events at the same vertical level (y-coordinate) as their last connected task.
13. CRITICAL: Implement the dynamic positioning system for all elements:
    - Follow the Dynamic Horizontal Positioning (X-coordinates) rules for task placement
    - Follow the Dynamic Vertical Positioning (Y-coordinates) rules for lane-based positioning
    - Use Cross-Lane Task Positioning Options for tasks that span multiple lanes
    - Apply Sequence Flow Connection Requirements for all connecting arrows
    - Achieve Visual Layout Goals for optimal diagram readability

CRITICAL REQUIREMENTS FOR SEQUENCE FLOWS AND CONNECTIONS (EXTREMELY IMPORTANT):
1. Use ONLY orthogonal routing (horizontal and vertical segments) for sequence flows.
2. NEVER use diagonal lines for sequence flows.
3. For sequence flows between elements in the same lane, use simple horizontal connections.
4. For sequence flows between elements in DIFFERENT LANES, follow these STRICT requirements:
   - Sequence flows MUST originate from the CENTER of the BOTTOM EDGE of the source task.
   - Sequence flows MUST connect to the CENTER of the TOP EDGE of the target task.
   - Use a simple vertical flow pattern with minimal horizontal segments.
   - Always use a top-to-bottom flow pattern for cross-lane connections.
5. Position tasks according to the dynamic positioning system:
   - Follow Dynamic Horizontal Positioning rules for x-coordinates
   - Follow Dynamic Vertical Positioning rules for y-coordinates based on lane assignment
   - Use Cross-Lane Task Positioning Options when tasks move between lanes
   - Maintain logical left-to-right flow progression as specified in Visual Layout Goals
6. All waypoints in BPMNEdge elements must have different coordinates (no identical x,y pairs).
7. Ensure proper spacing between parallel sequence flows.
8. Make sure sequence flows don't overlap with other elements.
9. For complex paths, use multiple waypoints to create clear orthogonal routing.
10. Create a COMPLETE flow path from the start event to the end event, ensuring:
    - The process flows logically across all lanes.
    - Each lane is properly connected to the next lane in the sequence.
    - The flow path is clear and easy to follow.
    - No disconnected elements or isolated tasks.
11. When connecting elements across different lanes, ensure the sequence flow:
    - Crosses lane boundaries at right angles.
    - Uses clear vertical segments when moving between lanes.
    - Maintains consistent spacing from other elements and flows.
12. Ensure proper cross-lane connections with the following requirements:
    - All connecting sequence flows must use orthogonal routing (horizontal and vertical segments only).
    - No diagonal lines should be used for sequence flows.
    - Sequence flows must cross lane boundaries at right angles.
    - Maintain consistent spacing between parallel flows.

The XML must be compatible with bpmn.js viewer and follow this structure:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI"
                  xmlns:dc="http://www.omg.org/spec/DD/********/DC"
                  xmlns:di="http://www.omg.org/spec/DD/********/DI"
                  id="Definitions_1"
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1" name="Process Name">
    <!-- Start with events and tasks first -->
    <bpmn:startEvent id="StartEvent_1"/>
    <bpmn:task id="Task_1" name="Receive and validate customer order details for processing">
      <bpmn:documentation>Receive and validate customer order details for processing. This task involves checking order information for completeness and accuracy before proceeding to the next step.</bpmn:documentation>
    </bpmn:task>
    <bpmn:task id="Task_2" name="Verify customer credit status and payment history">
      <bpmn:documentation>Verify customer credit status and payment history. This task involves checking the customer's credit rating and previous payment records to determine if the order can proceed.</bpmn:documentation>
    </bpmn:task>
    <bpmn:endEvent id="EndEvent_1"/>

    <!-- Use custom shapes for risk-control pairs -->
    <!-- Triangle shape for risks (using standard BPMN elements with custom rendering) -->
    <bpmn:task id="Risk_1" name="R1" />

    <!-- Diamond shape for controls (using standard BPMN elements with custom rendering) -->
    <bpmn:task id="Control_1" name="C1" />

    <!-- Each risk must be paired with exactly one control with matching numbers -->
    <!-- Risk triangles positioned above/left of tasks, control diamonds below/right of tasks -->
    <!-- Data store for system with connection to task -->
    <bpmn:dataStoreReference id="DataStore_1" name="System Name"/>

    <!-- Connection from task to system -->
    <bpmn:task id="Task_1">
      <bpmn:dataInputAssociation id="DataInputAssociation_1">
        <bpmn:sourceRef>DataStore_1</bpmn:sourceRef>
      </bpmn:dataInputAssociation>
    </bpmn:task>

    <!-- Include lanes in a laneSet with EXACTLY 2 lanes -->
    <bpmn:laneSet id="LaneSet_1">
      <bpmn:lane id="Lane_1" name="Department 1">
        <bpmn:flowNodeRef>StartEvent_1</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_1</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_2" name="Department 2">
        <bpmn:flowNodeRef>Task_2</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_1</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>

    <!-- Then add sequence flows after all elements are defined -->
    <!-- Complete flow path from start to end across all lanes -->
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="Task_1"/>
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_1" targetRef="Task_2"/>
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_2" targetRef="EndEvent_1"/>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <!-- Set diagram dimensions with landscape orientation and flexible sizing -->
      <!-- Using a width-to-height ratio of approximately 1.4:1 -->

      <!-- Lane shapes with dynamic sizing based on content and proper element containment -->
      <!-- Example for 2 lanes with calculated dimensions to contain all elements -->
      <bpmndi:BPMNShape id="Lane_1_di" bpmnElement="Lane_1" isHorizontal="true">
        <dc:Bounds x="150" y="50" width="1000" height="250"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_2_di" bpmnElement="Lane_2" isHorizontal="true">
        <dc:Bounds x="150" y="300" width="1000" height="250"/>
      </bpmndi:BPMNShape>

      <!-- Alternative example for 3 lanes with flexible dimensions -->
      <!--
      <bpmndi:BPMNShape id="Lane_1_di" bpmnElement="Lane_1" isHorizontal="true">
        <dc:Bounds x="150" y="50" width="900" height="200"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_2_di" bpmnElement="Lane_2" isHorizontal="true">
        <dc:Bounds x="150" y="250" width="900" height="200"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_3_di" bpmnElement="Lane_3" isHorizontal="true">
        <dc:Bounds x="150" y="450" width="900" height="200"/>
      </bpmndi:BPMNShape>
      -->

      <!-- Diagram elements with proper spacing -->
      <!-- Start event positioned using Dynamic Horizontal/Vertical Positioning (Lane 1 center y=175) -->
      <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="StartEvent_1">
        <dc:Bounds x="100" y="157" width="36" height="36"/>
      </bpmndi:BPMNShape>
      <!-- First task in first lane with dynamic sizing based on description length -->
      <bpmndi:BPMNShape id="Task_1_di" bpmnElement="Task_1">
        <dc:Bounds x="200" y="135" width="180" height="100"/>
      </bpmndi:BPMNShape>
      <!-- Task in second lane with proper containment and sizing -->
      <bpmndi:BPMNShape id="Task_2_di" bpmnElement="Task_2">
        <dc:Bounds x="400" y="375" width="180" height="100"/>
      </bpmndi:BPMNShape>
      <!-- End event positioned at rightmost x-coordinate in final lane (Lane 2 center y=425) -->
      <bpmndi:BPMNShape id="EndEvent_1_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="600" y="407" width="36" height="36"/>
      </bpmndi:BPMNShape>
      <!-- Triangle shape for risk (custom rendering) - positioned at the RIGHT TOP CORNER of task -->
      <bpmndi:BPMNShape id="Risk_1_di" bpmnElement="Risk_1">
        <dc:Bounds x="440" y="135" width="40" height="40"/>
        <!-- Custom property to indicate triangle shape -->
        <bpmndi:BPMNLabel>
          <dc:Bounds x="450" y="145" width="20" height="20"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>

      <!-- Diamond shape for control (custom rendering) - positioned at the RIGHT BOTTOM CORNER of task -->
      <bpmndi:BPMNShape id="Control_1_di" bpmnElement="Control_1">
        <dc:Bounds x="440" y="175" width="40" height="40"/>
        <!-- Custom property to indicate diamond shape -->
        <bpmndi:BPMNLabel>
          <dc:Bounds x="450" y="185" width="20" height="20"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>

      <!-- Data store positioned directly below its associated task -->
      <bpmndi:BPMNShape id="DataStore_1_di" bpmnElement="DataStore_1">
        <dc:Bounds x="355" y="300" width="50" height="50"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="356" y="357" width="48" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>

      <!-- Data association connecting task to system -->
      <bpmndi:BPMNEdge id="DataInputAssociation_1_di" bpmnElement="DataInputAssociation_1">
        <di:waypoint x="380" y="263" />
        <di:waypoint x="380" y="300" />
      </bpmndi:BPMNEdge>

      <!-- Edges with proper orthogonal waypoints using lane center positioning -->
      <!-- Horizontal flow in the same lane (start event to first task) - Lane 1 center y=175 -->
      <bpmndi:BPMNEdge id="Flow_1_di" bpmnElement="Flow_1">
        <di:waypoint x="136" y="175"/>
        <di:waypoint x="200" y="175"/>
      </bpmndi:BPMNEdge>
      <!-- Cross-lane flow using Sequence Flow Connection Requirements -->
      <bpmndi:BPMNEdge id="Flow_2_di" bpmnElement="Flow_2">
        <di:waypoint x="260" y="215"/>  <!-- Bottom center of Task_1 (Lane 1) -->
        <di:waypoint x="260" y="300"/>  <!-- Vertical segment to lane boundary -->
        <di:waypoint x="460" y="300"/>  <!-- Horizontal segment to target x -->
        <di:waypoint x="460" y="385"/>  <!-- Top center of Task_2 (Lane 2) -->
      </bpmndi:BPMNEdge>
      <!-- Horizontal flow in the same lane (second lane task to end event) - Lane 2 center y=425 -->
      <bpmndi:BPMNEdge id="Flow_3_di" bpmnElement="Flow_3">
        <di:waypoint x="520" y="425"/>
        <di:waypoint x="600" y="425"/>
      </bpmndi:BPMNEdge>
      <!-- No associations needed for risks and controls as they are positioned directly adjacent to tasks -->
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>"""
        print("Sending request to OpenAI to generate BPMN 2.0 XML...")

        # Generate the XML using OpenAI
        response = client.chat.completions.create(
            model=OPENAI_MODEL,
            temperature=OPENAI_TEMPERATURE,
            max_tokens=OPENAI_MAX_TOKENS,
            messages=[
                {"role": "system", "content": "You are a BPMN expert specializing in generating valid BPMN 2.0 XML diagrams."},
                {"role": "user", "content": prompt}
            ]
        )

        # Extract the XML content from the response
        if response.choices and len(response.choices) > 0:
            xml_content = response.choices[0].message.content
            print("Received BPMN XML content from OpenAI")

            # Clean up the XML (remove markdown code blocks if present) and extract the XML content
            if "```xml" in xml_content:
                xml_content = xml_content.split("```xml")[1].split("```")[0].strip()
            elif "```" in xml_content:
                xml_content = xml_content.split("```")[1].split("```")[0].strip()

            # Extract and validate the XML, passing the JSON structure for potential regeneration
            xml_content = extract_xml_content(xml_content, bpmn_json_structure)

            # Save the XML to a file
            process_name = bpmn_json_structure.get('process_name', 'bpmn_diagram')
            xml_file = f"{process_name.replace(' ', '_')}.bpmn"
            with open(xml_file, 'w') as f:
                f.write(xml_content)
            print(f"BPMN XML saved to {xml_file}")

            return xml_content
        else:
            print("No content in OpenAI response")
            raise Exception("OpenAI did not return any content")

    except Exception as e:
        print(f"Error with OpenAI API: {str(e)}")
        raise Exception(f"OpenAI API failed: {str(e)}")

def extract_xml_content(xml_content, bpmn_json_structure=None):
    """
    Extract and perform minimal cleanup of XML content from OpenAI API response.
    If validation fails, attempt to regenerate the XML using OpenAI with a focus on lane handling.

    Args:
        xml_content: Raw XML content from OpenAI API
        bpmn_json_structure: Optional JSON structure to use for regeneration if validation fails

    Returns:
        str: Extracted XML content
    """
    import re
    import xml.etree.ElementTree as ET

    # Remove any markdown code block formatting
    xml_content = re.sub(r'```xml\s*', '', xml_content)
    xml_content = re.sub(r'```\s*', '', xml_content)

    # Extract XML if it's embedded in other text
    xml_match = re.search(r'<\?xml.*?</bpmn:definitions>', xml_content, re.DOTALL)
    if xml_match:
        xml_content = xml_match.group(0)

    # Basic validation - check for well-formedness
    try:
        ET.fromstring(xml_content)
        print("XML validation successful")
    except Exception as e:
        print(f"XML validation failed: {str(e)}")

        # If we have the JSON structure, try to regenerate the XML with OpenAI
        if bpmn_json_structure:
            print("Attempting to regenerate XML with OpenAI focusing on lane handling...")
            try:
                # Create a specialized prompt for lane handling
                prompt = f"""
        You are a BPMN expert specializing in generating valid BPMN 2.0 XML diagrams with proper lane handling.

        {json.dumps(bpmn_json_structure, indent=2)}

        CRITICAL XML STRUCTURE REQUIREMENTS (EXTREMELY IMPORTANT):
        1. Create a complete and valid BPMN 2.0 XML document following the official BPMN 2.0 specification
        2. STRICTLY ADHERE to the official BPMN 2.0 XML schema (http://www.omg.org/spec/BPMN/********/MODEL)
        3. Follow the EXACT element order as specified in the BPMN 2.0 XML schema
        4. Include ALL required attributes for each element type as defined in the schema
        5. Use valid attribute values that conform to the schema's data type requirements
        6. Maintain proper parent-child relationships between elements as defined in the schema
        7. Ensure proper XML structure with correctly nested tags - every opening tag MUST have a matching closing tag
        8. All XML attributes MUST be properly quoted with double quotes
        9. Avoid self-closing tags for complex elements - use full opening and closing tags
        10. Ensure proper indentation and formatting for readability
        11. Include all necessary XML namespaces and schema references in the root element:
           - xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL"
           - xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI"
           - xmlns:dc="http://www.omg.org/spec/DD/********/DC"
           - xmlns:di="http://www.omg.org/spec/DD/********/DI"
           - xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           - xsi:schemaLocation="http://www.omg.org/spec/BPMN/********/MODEL http://www.omg.org/spec/BPMN/2.0/20100501/BPMN20.xsd"
        12. Ensure all elements are properly closed in the correct order (no overlapping tags)
        13. Do not include any custom or non-standard XML elements or attributes
        14. Validate the XML structure before returning it to ensure it is well-formed and schema-valid
        15. Ensure all IDs are unique and follow BPMN naming conventions
        16. Make sure all referenced elements exist in the XML
        17. Ensure all BPMNShape and BPMNEdge elements have a bpmnElement attribute referencing an existing element
        18. Ensure all sequence flows have both sourceRef and targetRef attributes pointing to existing elements
        19. All lanes must be contained within a laneSet element
        20. If using a participant, ensure it has a proper processRef attribute

        BPMN DIAGRAM CONTENT REQUIREMENTS:
        1. Create a process with the correct ID and name
        2. Include lanes for each department/role with the following STRICT constraints:
           - Each pool MUST contain a MINIMUM of 2 lanes
           - Each pool MUST contain a MAXIMUM of 3 lanes per pool
           - If the JSON structure has more than 3 lanes, combine related roles/departments to stay within the 3-lane limit
           - If the JSON structure has only 1 lane, create at least one additional lane
           - Lanes MUST ONLY represent process owners or departments (not systems or other entities)
           - Each lane MUST contain at least one task (NO EMPTY LANES allowed)
        3. Create tasks with DETAILED DESCRIPTIONS inside the task boxes:
           - Use the task's "name" field from the JSON structure as the primary content (this contains the task description)
           - Use the EXACT SAME TEXT for both the task name (bpmn:task name attribute) AND in the bpmn:documentation element
           - DO NOT use the "description" field as a separate element - the task description is already in the "name" field
           - Ensure the complete task description text is visible and properly formatted within each task element
           - Dynamically size each task element based on the length of its description text
           - Make task elements wider and/or taller as needed to accommodate longer descriptions
           - Use appropriate text size and wrapping to make descriptions readable
        4. Add EXACTLY ONE start event and EXACTLY ONE end event per pool with the following STRICT constraints:
           - Each pool MUST contain EXACTLY ONE start event and EXACTLY ONE end event - no more, no less
           - Position the start event in the FIRST lane whenever possible
           - Position the end event in the LAST lane whenever possible
           - Ensure clear flow from start to end across all relevant lanes
           - Tasks should be evenly distributed within their lanes
           - Maintain appropriate spacing between tasks (at least 50px)
        5. Connect all elements with sequence flows
        6. Include data stores for systems
        7. Represent risks as TRIANGLE-SHAPED elements with ONLY the risk ID number inside (no description)
        8. Represent controls as DIAMOND-SHAPED elements with ONLY the control ID number inside (no description)
        9. Position risk and control elements DIRECTLY ADJACENT to their associated tasks WITHOUT connecting lines
        10. Include appropriate documentation elements for descriptions
        9. Ensure all BPMNShape elements have proper dc:Bounds with x, y, width, and height attributes
        10. Ensure all BPMNEdge elements have at least two di:waypoint elements with x and y coordinates
        11. Make sure there are no NaN values in any coordinates
        12. For custom elements like risks, controls, and systems, use standard BPMN elements:
            - Each risk MUST be paired with exactly ONE control (1:1 relationship)
            - The risk ID and its corresponding control ID must always use the same number (R1 pairs with C1, R2 with C2, etc.)
            - For risks, use bpmn:task elements with custom rendering as RED TRIANGLES with ONLY the risk ID inside
            - For controls, use bpmn:task elements with custom rendering as GREEN DIAMONDS with ONLY the control ID inside
            - Position risk triangles ABOVE or to the LEFT of their associated tasks
            - Position control diamonds BELOW or to the RIGHT of their associated tasks
            - For systems:
              * Use bpmn:dataStoreReference elements (standard BPMN data store symbols)
              * ONLY include systems where they are explicitly mentioned for specific tasks
              * Connect each system to its relevant task with a direct dataInputAssociation arrow
              * Position systems directly below their associated tasks
            - Do NOT use connecting lines between risks/controls and their tasks
        13. ONLY use standard BPMN elements (do not create custom elements)
        14. All elements must be defined BEFORE they are referenced in sequence flows
        15. Follow bpmn-js library best practices (https://github.com/bpmn-io/bpmn-js) for optimal rendering

        LAYOUT REQUIREMENTS (EXTREMELY IMPORTANT):
        1. Use a landscape orientation for all diagrams with flexible dimensions
           - Do NOT constrain to exact A4 dimensions
           - Use a width-to-height ratio of approximately 1.4:1 (landscape orientation)
           - Allow the diagram to expand as needed based on content
        2. Divide the diagram into EQUAL lanes:
           - All lanes MUST have the SAME width spanning the entire diagram width (minus margins)
           - Lanes MUST be perfectly aligned with no gaps or overlaps between them
           - Distribute lane heights equally based on the number of lanes
        3. Use the following guidelines for lane dimensions:
           - Minimum lane height: 200px
           - Minimum lane width: 800px
           - Allow lanes to expand horizontally and vertically as needed
        4. Dynamically size task elements based on their content:
           - Base task element width and height on the length of the description text
           - For tasks with longer descriptions, increase the width and/or height proportionally
           - Minimum task width: 120px
           - Minimum task height: 80px
           - Maximum task width: 300px (use increased height for very long descriptions)
           - Ensure all text is visible and readable within the task element
           - Left margin for lane names: approximately 150px
        4. Increase spacing between elements to prevent overcrowding and ensure the process flow is clearly visible
        5. Ensure all text labels, task descriptions, and other elements are properly sized and positioned for readability
        6. Maintain appropriate margins around the entire diagram (at least 20px on all sides)
        7. Apply consistent spacing between connected elements to improve visual clarity (at least 50px between tasks)
        8. Position the start event at the left side of the diagram and the end event at the right side
        9. Ensure the diagram has a professional, balanced appearance with elements evenly distributed
        10. Position start events at the same vertical level (y-coordinate) as their first connected task
        11. Position end events at the same vertical level (y-coordinate) as their last connected task
        12. Follow bpmn-js rendering best practices for optimal visualization

        CRITICAL REQUIREMENTS FOR SEQUENCE FLOWS AND CONNECTIONS (EXTREMELY IMPORTANT):
        1. Use ONLY orthogonal routing (horizontal and vertical segments) for sequence flows
        2. NEVER use diagonal lines for sequence flows
        3. For sequence flows between elements in the same lane, use simple horizontal connections
        4. For sequence flows between elements in different lanes, use L-shaped or Z-shaped orthogonal paths with at least 3 waypoints
        5. Make sure the first waypoint of a sequence flow matches the source element's position
        6. Make sure the last waypoint of a sequence flow matches the target element's position
        7. All waypoints in BPMNEdge elements must have different coordinates (no identical x,y pairs)
        8. Ensure proper spacing between parallel sequence flows
        9. Make sure sequence flows don't overlap with other elements
        10. For complex paths, use multiple waypoints to create clear orthogonal routing
        11. Create a COMPLETE flow path from the start event to the end event, ensuring:
            - The process flows logically across all lanes
            - Each lane is properly connected to the next lane in the sequence
            - The flow path is clear and easy to follow
            - No disconnected elements or isolated tasks
        12. When connecting elements across different lanes, ensure the sequence flow:
            - Crosses lane boundaries at right angles
            - Uses clear vertical segments when moving between lanes
            - Maintains consistent spacing from other elements and flows
            - Follows the pattern shown in the reference diagram with proper vertical and horizontal segments
        13. Ensure proper cross-lane connections with the following requirements:
            - All connecting sequence flows must use orthogonal routing (horizontal and vertical segments only)
            - No diagonal lines should be used for sequence flows
            - Sequence flows must cross lane boundaries at right angles
            - Maintain consistent spacing between parallel flows

        The XML must be compatible with bpmn.js viewer and follow this structure:
        ```xml
        <?xml version="1.0" encoding="UTF-8"?>
        <bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL"
                          xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI"
                          xmlns:dc="http://www.omg.org/spec/DD/********/DC"
                          xmlns:di="http://www.omg.org/spec/DD/********/DI"
                          id="Definitions_1"
                          targetNamespace="http://bpmn.io/schema/bpmn">
          <bpmn:process id="Process_1" name="Process Name">
            <!-- Start with events and tasks first -->
            <bpmn:startEvent id="StartEvent_1"/>
            <bpmn:task id="Task_1" name="Receive and validate customer order details for processing">
              <bpmn:documentation>Receive and validate customer order details for processing. This task involves checking order information for completeness and accuracy before proceeding to the next step.</bpmn:documentation>
            </bpmn:task>
            <bpmn:task id="Task_2" name="Verify customer credit status and payment history">
              <bpmn:documentation>Verify customer credit status and payment history. This task involves checking the customer's credit rating and previous payment records to determine if the order can proceed.</bpmn:documentation>
            </bpmn:task>
            <bpmn:endEvent id="EndEvent_1"/>

            <!-- Use custom shapes for risk-control pairs -->
            <!-- Triangle shape for risks (using standard BPMN elements with custom rendering) -->
            <bpmn:task id="Risk_1" name="R1" />

            <!-- Diamond shape for controls (using standard BPMN elements with custom rendering) -->
            <bpmn:task id="Control_1" name="C1" />

            <!-- Each risk must be paired with exactly one control with matching numbers -->
            <!-- Risk triangles positioned at the RIGHT TOP CORNER of tasks, control diamonds at the RIGHT BOTTOM CORNER of tasks -->
            <!-- Data store for system with connection to task -->
            <bpmn:dataStoreReference id="DataStore_1" name="System Name"/>

            <!-- Connection from task to system -->
            <bpmn:task id="Task_1">
              <bpmn:dataInputAssociation id="DataInputAssociation_1">
                <bpmn:sourceRef>DataStore_1</bpmn:sourceRef>
              </bpmn:dataInputAssociation>
            </bpmn:task>

            <!-- Include lanes in a laneSet with EXACTLY 2 lanes -->
            <bpmn:laneSet id="LaneSet_1">
              <bpmn:lane id="Lane_1" name="Department 1">
                <bpmn:flowNodeRef>StartEvent_1</bpmn:flowNodeRef>
                <bpmn:flowNodeRef>Task_1</bpmn:flowNodeRef>
              </bpmn:lane>
              <bpmn:lane id="Lane_2" name="Department 2">
                <bpmn:flowNodeRef>Task_2</bpmn:flowNodeRef>
                <bpmn:flowNodeRef>EndEvent_1</bpmn:flowNodeRef>
              </bpmn:lane>
            </bpmn:laneSet>

            <!-- Then add sequence flows after all elements are defined -->
            <!-- Complete flow path from start to end across all lanes -->
            <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="Task_1"/>
            <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_1" targetRef="Task_2"/>
            <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_2" targetRef="EndEvent_1"/>
          </bpmn:process>
          <bpmndi:BPMNDiagram id="BPMNDiagram_1">
            <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
              <!-- Set diagram dimensions for A4 landscape (297mm × 210mm) -->
              <!-- Using pixels: approximately 1122px × 793px (96 DPI) -->

              <!-- Lane shapes with EXACTLY equal height distribution on A4 landscape -->
              <!-- Example for 2 lanes (each exactly 50% of available height) -->
              <bpmndi:BPMNShape id="Lane_1_di" bpmnElement="Lane_1" isHorizontal="true">
                <dc:Bounds x="160" y="50" width="962" height="346.5"/>
              </bpmndi:BPMNShape>
              <bpmndi:BPMNShape id="Lane_2_di" bpmnElement="Lane_2" isHorizontal="true">
                <dc:Bounds x="160" y="396.5" width="962" height="346.5"/>
              </bpmndi:BPMNShape>

              <!-- Alternative example for 3 lanes (each exactly 33.33% of available height) -->
              <!--
              <bpmndi:BPMNShape id="Lane_1_di" bpmnElement="Lane_1" isHorizontal="true">
                <dc:Bounds x="160" y="50" width="962" height="231"/>
              </bpmndi:BPMNShape>
              <bpmndi:BPMNShape id="Lane_2_di" bpmnElement="Lane_2" isHorizontal="true">
                <dc:Bounds x="160" y="281" width="962" height="231"/>
              </bpmndi:BPMNShape>
              <bpmndi:BPMNShape id="Lane_3_di" bpmnElement="Lane_3" isHorizontal="true">
                <dc:Bounds x="160" y="512" width="962" height="231"/>
              </bpmndi:BPMNShape>
              -->

              <!-- Diagram elements with proper spacing -->
              <!-- Start event positioned in the FIRST lane -->
              <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="StartEvent_1">
                <dc:Bounds x="220" y="205" width="36" height="36"/>
              </bpmndi:BPMNShape>
              <!-- Task in first lane -->
              <bpmndi:BPMNShape id="Task_1_di" bpmnElement="Task_1">
                <dc:Bounds x="320" y="183" width="120" height="80"/>
              </bpmndi:BPMNShape>
              <!-- Task in second lane -->
              <bpmndi:BPMNShape id="Task_2_di" bpmnElement="Task_2">
                <dc:Bounds x="320" y="530" width="120" height="80"/>
              </bpmndi:BPMNShape>
              <!-- End event positioned in the LAST lane -->
              <bpmndi:BPMNShape id="EndEvent_1_di" bpmnElement="EndEvent_1">
                <dc:Bounds x="500" y="552" width="36" height="36"/>
              </bpmndi:BPMNShape>
              <!-- Triangle shape for risk (custom rendering) - positioned at the RIGHT TOP CORNER of task -->
              <bpmndi:BPMNShape id="Risk_1_di" bpmnElement="Risk_1">
                <dc:Bounds x="440" y="135" width="40" height="40"/>
                <!-- Custom property to indicate triangle shape -->
                <bpmndi:BPMNLabel>
                  <dc:Bounds x="450" y="145" width="20" height="20"/>
                </bpmndi:BPMNLabel>
              </bpmndi:BPMNShape>

              <!-- Diamond shape for control (custom rendering) - positioned at the RIGHT BOTTOM CORNER of task -->
              <bpmndi:BPMNShape id="Control_1_di" bpmnElement="Control_1">
                <dc:Bounds x="440" y="175" width="40" height="40"/>
                <!-- Custom property to indicate diamond shape -->
                <bpmndi:BPMNLabel>
                  <dc:Bounds x="450" y="185" width="20" height="20"/>
                </bpmndi:BPMNLabel>
              </bpmndi:BPMNShape>

              <!-- Data store positioned directly below its associated task -->
              <bpmndi:BPMNShape id="DataStore_1_di" bpmnElement="DataStore_1">
                <dc:Bounds x="355" y="300" width="50" height="50"/>
                <bpmndi:BPMNLabel>
                  <dc:Bounds x="356" y="357" width="48" height="14"/>
                </bpmndi:BPMNLabel>
              </bpmndi:BPMNShape>

              <!-- Data association connecting task to system -->
              <bpmndi:BPMNEdge id="DataInputAssociation_1_di" bpmnElement="DataInputAssociation_1">
                <di:waypoint x="380" y="263" />
                <di:waypoint x="380" y="300" />
              </bpmndi:BPMNEdge>

              <!-- Edges with proper orthogonal waypoints -->
              <!-- Horizontal flow in the same lane (start event to first task) -->
              <bpmndi:BPMNEdge id="Flow_1_di" bpmnElement="Flow_1">
                <di:waypoint x="256" y="175"/>
                <di:waypoint x="320" y="175"/>
              </bpmndi:BPMNEdge>

              <!-- Cross-lane connection from task in first lane to task in second lane -->
              <!-- Note the sequence flow originates from the CENTER of the BOTTOM EDGE of Task_1 -->
              <!-- And connects to the CENTER of the TOP EDGE of Task_2 -->
              <bpmndi:BPMNEdge id="Flow_2_di" bpmnElement="Flow_2">
                <di:waypoint x="380" y="215"/> <!-- Bottom center of Task_1 -->
                <di:waypoint x="380" y="300"/> <!-- Vertical segment crossing lane boundary -->
                <di:waypoint x="380" y="385"/> <!-- Top center of Task_2 -->
              </bpmndi:BPMNEdge>

              <!-- Horizontal flow in the same lane (second task to end event) -->
              <bpmndi:BPMNEdge id="Flow_3_di" bpmnElement="Flow_3">
                <di:waypoint x="440" y="425"/>
                <di:waypoint x="500" y="425"/>
              </bpmndi:BPMNEdge>
              <!-- No associations needed for risks and controls as they are positioned directly adjacent to tasks -->
            </bpmndi:BPMNPlane>
          </bpmndi:BPMNDiagram>
        </bpmn:definitions>
        ```

        FINAL VALIDATION CHECKLIST (VERIFY BEFORE RETURNING):
        1. The XML is well-formed with properly nested tags
        2. All opening tags have matching closing tags
        3. All attributes are properly quoted
        4. All referenced elements exist in the XML
        5. The pool contains a MINIMUM of 2 lanes and a MAXIMUM of 3 lanes
        6. Each lane contains AT LEAST ONE task (no empty lanes)
        7. Lanes ONLY represent process owners or departments (not systems or other entities)
        8. The pool contains EXACTLY ONE start event and EXACTLY ONE end event - no more, no less
        9. The start event is positioned in the FIRST lane whenever possible
        10. The end event is positioned in the LAST lane whenever possible
        11. Lanes are equally distributed with flexible dimensions:
        12. Task elements are properly sized based on their description length:
            - All lanes have equal height
            - Maintain landscape orientation with width-to-height ratio of approximately 1.4:1
            - Allow diagram to expand as needed based on content
        12. Tasks are evenly distributed within their lanes with appropriate spacing (at least 50px)
        13. Start and end events are properly positioned at the same vertical level as their connected tasks
        14. All sequence flows use orthogonal routing with horizontal and vertical segments only
        15. No diagonal lines are used for sequence flows
        16. Sequence flows cross lane boundaries at right angles
        17. For cross-lane connections, sequence flows originate from the CENTER of the BOTTOM EDGE of source tasks
        18. Tasks in adjacent lanes are positioned with vertical alignment (directly below or slightly to the right)
        19. Risk triangles are positioned at the RIGHT TOP CORNER of their associated tasks
        20. Control diamonds are positioned at the RIGHT BOTTOM CORNER of their associated tasks
        21. There is a COMPLETE flow path from start event to end event across all relevant lanes
        22. All BPMNShape and BPMNEdge elements have proper bpmnElement attributes
        23. All waypoints in sequence flows have different coordinates
        24. The XML follows the BPMN 2.0 specification and is compatible with bpmn-js library

        Return ONLY the complete BPMN 2.0 XML document without any markdown formatting or explanation.
        The XML should be properly formatted with correct indentation and should be valid according to the BPMN 2.0 schema.
        """
                 #Generate the XML using OpenAI
                response = client.chat.completions.create(
                    model=OPENAI_MODEL,
                    temperature=0.1,  # Lower temperature for more precise output
                    max_tokens=OPENAI_MAX_TOKENS,
                    messages=[
                        {"role": "system", "content": "You are a BPMN expert specializing in generating valid BPMN 2.0 XML diagrams with proper lane handling."},
                        {"role": "user", "content": prompt}
                    ]
                )

                # Extract the XML content from the response
                if response.choices and len(response.choices) > 0:
                    regenerated_xml = response.choices[0].message.content
                    print("Successfully regenerated XML with OpenAI")

                    # Clean up the regenerated XML
                    regenerated_xml = re.sub(r'```xml\s*', '', regenerated_xml)
                    regenerated_xml = re.sub(r'```\s*', '', regenerated_xml)

                    # Extract XML if it's embedded in other text
                    xml_match = re.search(r'<\?xml.*?</bpmn:definitions>', regenerated_xml, re.DOTALL)
                    if xml_match:
                        regenerated_xml = xml_match.group(0)

                    # Validate the regenerated XML
                    try:
                        ET.fromstring(regenerated_xml)
                        print("Regenerated XML validation successful")
                        return regenerated_xml
                    except Exception as regen_error:
                        print(f"Regenerated XML validation also failed: {str(regen_error)}")
                        # Fall through to the default fallback
                else:
                    print("No content in OpenAI regeneration response")
            except Exception as regen_api_error:
                print(f"Error with OpenAI API during regeneration: {str(regen_api_error)}")

        # If regeneration failed or wasn't attempted, provide a minimal valid BPMN XML as fallback
        print("Generating minimal valid BPMN XML")
        xml_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI"
                  xmlns:dc="http://www.omg.org/spec/DD/********/DC"
                  xmlns:di="http://www.omg.org/spec/DD/********/DI"
                  id="Definitions_1"
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1" isExecutable="false">
    <bpmn:laneSet id="LaneSet_1">
      <bpmn:lane id="Lane_1" name="Department 1">
        <bpmn:flowNodeRef>StartEvent_1</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_1</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_2" name="Department 2">
        <bpmn:flowNodeRef>Task_2</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_1</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:startEvent id="StartEvent_1"/>
    <bpmn:task id="Task_1" name="Placeholder Task 1"/>
    <bpmn:task id="Task_2" name="Placeholder Task 2"/>
    <bpmn:endEvent id="EndEvent_1"/>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="Task_1"/>
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_1" targetRef="Task_2"/>
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_2" targetRef="EndEvent_1"/>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <!-- Lanes with dynamic sizing and proper element containment -->
      <bpmndi:BPMNShape id="Lane_1_di" bpmnElement="Lane_1" isHorizontal="true">
        <dc:Bounds x="150" y="50" width="800" height="250"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_2_di" bpmnElement="Lane_2" isHorizontal="true">
        <dc:Bounds x="150" y="300" width="800" height="250"/>
      </bpmndi:BPMNShape>

      <!-- Elements using Dynamic Positioning System with Lane Center Coordinates -->
      <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="StartEvent_1">
        <dc:Bounds x="100" y="157" width="36" height="36"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_1_di" bpmnElement="Task_1">
        <dc:Bounds x="200" y="135" width="150" height="80"/>
      </bpmndi:BPMNShape>

      <!-- Elements in second lane using Cross-Lane Task Positioning with proper sizing -->
      <bpmndi:BPMNShape id="Task_2_di" bpmnElement="Task_2">
        <dc:Bounds x="400" y="385" width="150" height="80"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="600" y="407" width="36" height="36"/>
      </bpmndi:BPMNShape>

      <!-- Sequence flows using Lane Center Positioning -->
      <bpmndi:BPMNEdge id="Flow_1_di" bpmnElement="Flow_1">
        <di:waypoint x="136" y="175"/>
        <di:waypoint x="200" y="175"/>
      </bpmndi:BPMNEdge>

      <!-- Cross-lane connection using Sequence Flow Connection Requirements -->
      <bpmndi:BPMNEdge id="Flow_2_di" bpmnElement="Flow_2">
        <di:waypoint x="250" y="215"/> <!-- Bottom center of Task_1 (Lane 1) -->
        <di:waypoint x="250" y="280"/> <!-- Vertical segment to lane boundary -->
        <di:waypoint x="450" y="280"/> <!-- Horizontal segment to target x -->
        <di:waypoint x="450" y="385"/> <!-- Top center of Task_2 (Lane 2) -->
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="Flow_3_di" bpmnElement="Flow_3">
        <di:waypoint x="500" y="425"/>
        <di:waypoint x="600" y="425"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>"""

    return xml_content


def generate_bpmn_diagram(subprocess_data):
    """
    Generate a BPMN diagram for a subprocess using OpenAI for JSON structure and OpenAI for XML generation.
    Uses pre-generated JSON structure if available.

    Args:
        subprocess_data: Structured data for the subprocess

    Returns:
        dict: Dictionary with BPMN diagram information
    """
    try:
        # Extract process name for reference
        process_name = subprocess_data.get('subprocess_name', 'Business Process')

        # Check if pre-generated BPMN JSON structure is available
        if 'bpmn_json_structure' in subprocess_data and subprocess_data['bpmn_json_structure']:
            print(f"Using pre-generated BPMN JSON structure for {process_name}...")
            bpmn_json_structure = subprocess_data['bpmn_json_structure']
            json_file = subprocess_data.get('bpmn_json_file')
        else:
            # Generate JSON structure using OpenAI if not pre-generated
            print(f"No pre-generated structure found. Generating BPMN JSON structure for {process_name}...")
            bpmn_json_structure = generate_bpmn_json_structure(subprocess_data)
            # Save the JSON structure for reference
            json_file = f"{process_name.replace(' ', '_')}_structure.json"
            with open(json_file, 'w') as f:
                json.dump(bpmn_json_structure, f, indent=2)
            print(f"JSON structure saved to {json_file}")

        # Generate BPMN XML using OpenAI
        print(f"Generating BPMN XML for {process_name}...")
        try:
            xml_content = generate_bpmn_xml(bpmn_json_structure)
        except Exception as xml_error:
            # Check if the error is related to lane validation
            error_str = str(xml_error).lower()
            if any(term in error_str for term in ['lane', 'flownode', 'reference', 'validation']):
                print(f"Lane-specific XML validation issue detected: {error_str}")
                print("Attempting direct XML generation with OpenAI focusing on lane handling...")

                # Create a specialized prompt for lane handling
                lane_focused_prompt = f"""
                You are a BPMN XML expert specializing in generating valid BPMN 2.0 XML diagrams with proper lane handling.

                Create a valid BPMN 2.0 XML document based on the following JSON structure, with special attention to proper lane handling:

                {json.dumps(bpmn_json_structure, indent=2)}

                CRITICAL LANE HANDLING REQUIREMENTS:
                1. All lanes MUST be contained within a laneSet element
                2. Each lane MUST have a unique ID
                3. Each lane MUST have proper flowNodeRef elements for all tasks, events, and gateways in that lane
                4. Ensure all referenced elements exist in the XML before they are referenced
                5. Ensure proper nesting of all XML elements
                6. Ensure all opening tags have matching closing tags
                7. Ensure all attributes are properly quoted with double quotes
                8. Ensure all BPMNShape elements for lanes have proper dc:Bounds with x, y, width, and height attributes
                9. For task elements:
                   - Use the task's "name" field from the JSON structure as the primary content (this contains the task description)
                   - Use the EXACT SAME TEXT for both the task name (bpmn:task name attribute) AND in the bpmn:documentation element
                   - DO NOT use the "description" field as a separate element - the task description is already in the "name" field
                10. Use a landscape orientation for all diagrams with flexible dimensions:
                   - Do NOT constrain to exact A4 dimensions
                   - Use a width-to-height ratio of approximately 1.4:1 (landscape orientation)
                   - Allow the diagram to expand as needed based on content
                11. Divide the diagram into EQUAL lanes:
                   - All lanes MUST have the SAME width spanning the entire diagram width (minus margins)
                   - Lanes MUST be perfectly aligned with no gaps or overlaps between them
                   - Distribute lane heights equally based on the number of lanes
                12. Each pool MUST contain a MINIMUM of 2 lanes and a MAXIMUM of 3 lanes per pool
                13. Lanes MUST ONLY represent process owners or departments (not systems or other entities)
                14. Each lane MUST contain at least one task (NO EMPTY LANES allowed)
                15. Do NOT assume start/end events as separate lanes
                16. Each pool MUST contain EXACTLY ONE start event and EXACTLY ONE end event - no more, no less
                17. Position the start event in the FIRST lane whenever possible
                18. Position the end event in the LAST lane whenever possible
                19. CRITICAL LAYOUT AND POSITIONING REQUIREMENTS FOR BPMN ELEMENTS:

        1. **Dynamic Horizontal Positioning (X-coordinates)**:
           - Start event: x=100, y=100 (positioned in first lane)
           - First task in first lane: x=200, y=100
           - Subsequent tasks in same lane: increment x by 200px (x=400, x=600, x=800, etc.)
           - Tasks in different lanes: maintain horizontal progression OR position directly below previous lane's task
           - End event: position at the rightmost x-coordinate after all tasks are placed

        2. **Dynamic Vertical Positioning (Y-coordinates) - Enhanced:**
           - Lane 1: All elements positioned at y=175 (center of lane with bounds y=50 to y=300)
           - Lane 2: All elements positioned at y=425 (center of lane with bounds y=300 to y=550)
           - Lane 3: All elements positioned at y=675 (center of lane with bounds y=550 to y=800)
           - Continue this pattern for additional lanes, calculating the center y-coordinate of each lane's vertical bounds

           **Lane Center Calculation Formula:**
           - Lane center y-coordinate = (lane_top_y + lane_bottom_y) / 2
           - Ensure all tasks, events, and other elements within a lane use the same center y-coordinate
           - This creates perfect horizontal alignment of all elements within each lane
           - Maintain the 200px minimum spacing between lane boundaries while using center positioning

        3. **Cross-Lane Task Positioning Options**:
           - Option A: Continue horizontal progression (if task moves to next lane, use next available x-coordinate like x=600, y=425)
           - Option B: Vertical alignment (position task directly below the last task from previous lane, e.g., x=400, y=425)
           - Always use the target lane's center y-coordinate (y=175 for Lane 1, y=425 for Lane 2, y=675 for Lane 3)

        4. **Sequence Flow Connection Requirements**:
           - Ensure all connecting arrows (sequence flows) use proper waypoints that create clean orthogonal paths
           - For cross-lane connections: flow from bottom center of source task to top center of target task
           - Maintain consistent spacing and avoid overlapping flows
           - Use multiple waypoints for complex routing to ensure visual clarity

        5. **Visual Layout Goals**:
           - Create a logical left-to-right flow progression
           - Ensure no overlapping elements or awkward positioning
           - Maintain consistent spacing between all elements (minimum 50px)
           - Position end event as the rightmost element in the final lane
           - Ensure all elements are properly aligned within their respective lanes

        6. **CRITICAL BPMN LAYOUT AND ELEMENT SIZING REQUIREMENTS:**

           **Dynamic Lane Sizing and Element Containment:**
           - Ensure all BPMN elements (start events, tasks, gateways, end events) are completely contained within their respective lane boundaries
           - No element should extend outside or overlap with lane borders
           - Lanes should dynamically expand their width and height based on the content they contain
           - Calculate minimum lane dimensions based on the number and size of elements within each lane
           - Add appropriate padding (minimum 20px) between lane borders and contained elements

           **Task Element Sizing and Content Display:**
           - Dynamically size task elements based on the length of their description text
           - Display task descriptions (not task titles) inside the task elements as the primary content
           - Ensure task descriptions are fully visible and readable within the task boundaries
           - Implement text wrapping and appropriate font sizing to fit descriptions within task elements
           - Minimum task size: 120px width × 80px height
           - Maximum task width: 300px (increase height for longer descriptions)

           **Element Overlap Prevention:**
           - Implement collision detection to prevent any BPMN elements from overlapping
           - Maintain minimum spacing of 50px between adjacent elements
           - Ensure sequence flows do not overlap with other elements
           - Adjust element positioning automatically if overlaps are detected

           **Layout Analysis and Strategy Optimization:**
           - Analyze the current xy positioning strategy for visual clarity and effectiveness
           - If the current lane center positioning creates layout issues, implement alternative positioning strategies
           - Consider horizontal flow optimization to prevent overcrowding
           - Implement intelligent spacing algorithms that adapt to content density
                20. Dynamically size task elements based on their content:
                   - Base task element width and height on the length of the description text
                   - For tasks with longer descriptions, increase the width and/or height proportionally
                   - Minimum task width: 120px
                   - Minimum task height: 80px
                   - Maximum task width: 300px (use increased height for very long descriptions)
                   - Ensure all text is visible and readable within the task element

                CRITICAL SEQUENCE FLOW REQUIREMENTS:
                1. Use ONLY orthogonal routing (horizontal and vertical segments) for sequence flows
                2. NEVER use diagonal lines for sequence flows
                3. For sequence flows between elements in DIFFERENT LANES:
                   - Sequence flows MUST originate from the CENTER of the BOTTOM EDGE of the source task
                   - Sequence flows MUST connect to the CENTER of the TOP EDGE of the target task
                   - Use a simple vertical flow pattern with minimal horizontal segments
                4. Position tasks according to the dynamic positioning system:
                   - Follow Dynamic Horizontal Positioning rules for x-coordinates
                   - Follow Dynamic Vertical Positioning rules for y-coordinates based on lane assignment
                   - Use Cross-Lane Task Positioning Options when tasks move between lanes
                   - Maintain logical left-to-right flow progression as specified in Visual Layout Goals
                5. Sequence flows must cross lane boundaries at right angles
                6. Maintain consistent spacing between parallel flows
                7. Create a COMPLETE flow path from start event to end event across all relevant lanes

                CRITICAL RISK-CONTROL HANDLING REQUIREMENTS:
                1. Each risk MUST be paired with exactly ONE control (1:1 relationship)
                2. The risk ID and its corresponding control ID must always use the same number (R1 pairs with C1, R2 with C2, etc.)
                3. Position risk triangles ABOVE or to the LEFT of their associated tasks
                4. Position control diamonds BELOW or to the RIGHT of their associated tasks
                5. Do NOT use connecting lines between risks/controls and their tasks

                Return ONLY the complete BPMN 2.0 XML document without any markdown formatting or explanation.
                """

                # Generate the XML directly using OpenAI
                response = client.chat.completions.create(
                    model=OPENAI_MODEL,
                    temperature=0.1,  # Lower temperature for more precise output
                    max_tokens=OPENAI_MAX_TOKENS,
                    messages=[
                        {"role": "system", "content": "You are a BPMN expert specializing in generating valid BPMN 2.0 XML diagrams with proper lane handling."},
                        {"role": "user", "content": lane_focused_prompt}
                    ]
                )

                # Extract the XML content from the response
                if response.choices and len(response.choices) > 0:
                    xml_content = response.choices[0].message.content
                    print("Successfully generated XML with OpenAI focusing on lane handling")

                    # Clean up the XML
                    import re
                    xml_content = re.sub(r'```xml\s*', '', xml_content)
                    xml_content = re.sub(r'```\s*', '', xml_content)

                    # Extract XML if it's embedded in other text
                    xml_match = re.search(r'<\?xml.*?</bpmn:definitions>', xml_content, re.DOTALL)
                    if xml_match:
                        xml_content = xml_match.group(0)
                else:
                    # If direct generation fails, re-raise the original error
                    raise xml_error
            else:
                # If not a lane-specific issue, re-raise the original error
                raise xml_error

        # Create a simple preview image for the diagram
        width, height = 800, 200
        image = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(image)

        # Try to load fonts
        try:
            title_font = ImageFont.truetype("arial.ttf", 20)
            normal_font = ImageFont.truetype("arial.ttf", 14)
        except IOError:
            # Fallback to default font if arial is not available
            title_font = ImageFont.load_default()
            normal_font = ImageFont.load_default()

        # Draw title and info
        draw.text((width//2 - 200, 30), f"BPMN Diagram: {process_name}", fill="black", font=title_font)
        draw.text((width//2 - 200, 80), "Interactive BPMN diagram available in the viewer", fill="black", font=normal_font)
        draw.text((width//2 - 200, 120), f"BPMN XML file: {process_name.replace(' ', '_')}.bpmn", fill="black", font=normal_font)

        # Return diagram information
        return {
            'image': image,
            'xml_file': f"{process_name.replace(' ', '_')}.bpmn",
            'json_file': json_file,
            'process_name': process_name,
            'xml_content': xml_content  # Include the XML content in the return value
        }
    except Exception as e:
        print(f"Error generating BPMN diagram: {str(e)}")
        # Create a simple error message image
        width, height = 800, 200
        image = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(image)

        # Try to load fonts
        try:
            title_font = ImageFont.truetype("arial.ttf", 20)
            normal_font = ImageFont.truetype("arial.ttf", 14)
        except IOError:
            # Fallback to default font if arial is not available
            title_font = ImageFont.load_default()
            normal_font = ImageFont.load_default()

        # Draw error message
        process_name = subprocess_data.get('subprocess_name', 'Unknown Process')
        draw.text((width//2 - 200, 30), f"Error generating BPMN diagram: {process_name}", fill="red", font=title_font)
        draw.text((width//2 - 200, 80), "Please try again or check your data format.", fill="black", font=normal_font)
        draw.text((width//2 - 200, 120), f"Error: {str(e)}", fill="black", font=normal_font)

        return {
            'image': image,
            'xml_file': None,
            'json_file': None,
            'process_name': process_name
        }



def wrap_text(text, font, max_width):
    """Helper function to wrap text within a given width"""
    words = text.split()
    lines = []
    current_line = []

    for word in words:
        test_line = ' '.join(current_line + [word])
        # Use getsize for older PIL versions
        try:
            width = font.getsize(test_line)[0]
        except:
            # For newer PIL versions
            try:
                width = font.getlength(test_line)
            except:
                width = len(test_line) * 7  # Rough estimate if all else fails

        if width <= max_width:
            current_line.append(word)
        else:
            lines.append(' '.join(current_line))
            current_line = [word]

    if current_line:
        lines.append(' '.join(current_line))

    # Limit to 3 lines to avoid overflow
    if len(lines) > 3:
        lines = lines[:2]
        lines.append(lines[1] + "...")

    return lines

def generate_all_bpmn_diagrams(subprocess_analyses):
    """
    Generate BPMN diagrams for all subprocesses with global sequential risk-control numbering.

    Args:
        subprocess_analyses: Dictionary of structured data for all subprocesses

    Returns:
        dict: Dictionary of generated BPMN diagram information for all subprocesses
    """
    diagrams = {}

    # Handle case where subprocess_analyses is empty or None
    if not subprocess_analyses:
        # Create a default diagram
        image = Image.new('RGB', (800, 400), color='white')
        draw = ImageDraw.Draw(image)
        draw.text((100, 150), "No subprocess data available", fill="black")
        draw.text((100, 200), "Please check your RCM data and try again.", fill="black")
        diagrams["Default Process"] = {
            'image': image,
            'xml_file': None,
            'json_file': None,
            'process_name': "Default Process"
        }
        return diagrams

    # Initialize global risk-control counter for sequential numbering across all pools
    global_risk_control_counter = 1

    # First pass: Update risk and control IDs with global sequential numbering
    print("Implementing global sequential risk-control numbering across all pools...")
    for subprocess_name, subprocess_data in subprocess_analyses.items():
        if isinstance(subprocess_data, dict) and "lanes" in subprocess_data:
            for lane in subprocess_data["lanes"]:
                if "tasks" in lane:
                    for task in lane["tasks"]:
                        # Update risk-control pairs with global sequential numbering
                        if "risks" in task and len(task["risks"]) > 0:
                            # Create new risk-control pairs with global sequential numbering
                            new_risk_control_pairs = []

                            # Process each risk
                            for risk in task["risks"]:
                                # Create a new risk with global ID
                                new_risk = {
                                    "risk_id": f"R{global_risk_control_counter}",
                                    "risk_description": risk.get("risk_description", "")
                                }

                                # Create a corresponding control with matching global ID
                                new_control = {
                                    "control_id": f"C{global_risk_control_counter}",
                                    "control_description": f"Control for Risk R{global_risk_control_counter}"
                                }

                                # Add the pair to the new list
                                new_risk_control_pairs.append((new_risk, new_control))

                                # Increment the global counter
                                global_risk_control_counter += 1

                            # Replace the risks array with the new risks
                            task["risks"] = [pair[0] for pair in new_risk_control_pairs]

                            # Replace or create the controls array with the new controls
                            task["controls"] = [pair[1] for pair in new_risk_control_pairs]

    print(f"Global risk-control numbering complete. Total pairs: {global_risk_control_counter-1}")

    # Process each subprocess to generate diagrams
    for subprocess_name, subprocess_data in subprocess_analyses.items():
        try:
            # Generate diagram using pre-generated JSON structure (if available) and OpenAI for XML
            print(f"Generating BPMN diagram for subprocess: {subprocess_name}")
            # Check if the subprocess data already has a pre-generated BPMN JSON structure
            if 'bpmn_json_structure' in subprocess_data and subprocess_data['bpmn_json_structure']:
                print(f"Using pre-generated BPMN JSON structure for {subprocess_name}...")
            else:
                print(f"No pre-generated structure found for {subprocess_name}. Will generate one...")

            diagram_info = generate_bpmn_diagram(subprocess_data)
            diagrams[subprocess_name] = diagram_info

        except Exception as e:
            # Log the error and create a simple error image
            print(f"Error generating diagram for subprocess '{subprocess_name}': {str(e)}")

            # Create a very basic error image
            image = Image.new('RGB', (800, 400), color='white')
            draw = ImageDraw.Draw(image)

            # Try to load fonts
            try:
                title_font = ImageFont.truetype("arial.ttf", 20)
                normal_font = ImageFont.truetype("arial.ttf", 14)
            except IOError:
                # Fallback to default font if arial is not available
                title_font = ImageFont.load_default()
                normal_font = ImageFont.load_default()

            draw.text((100, 150), f"Diagram generation failed for: {subprocess_name}", fill="red", font=title_font)
            draw.text((100, 200), "Please try again or check your data format.", fill="black", font=normal_font)
            draw.text((100, 250), f"Error: {str(e)}", fill="black", font=normal_font)

            diagrams[subprocess_name] = {
                'image': image,
                'xml_file': None,
                'json_file': None,
                'process_name': subprocess_name
            }

    # If no diagrams were generated, create a default one
    if not diagrams:
        image = Image.new('RGB', (800, 400), color='white')
        draw = ImageDraw.Draw(image)
        draw.text((100, 150), "No diagrams could be generated", fill="black")
        draw.text((100, 200), "Please check your RCM data and try again.", fill="black")
        diagrams["Default Process"] = {
            'image': image,
            'xml_file': None,
            'json_file': None,
            'process_name': "Default Process"
        }

    return diagrams

def image_to_base64(image):
    """
    Convert a PIL Image to a base64-encoded string.

    Args:
        image: PIL Image

    Returns:
        str: Base64-encoded image string
    """
    buffered = BytesIO()
    image.save(buffered, format="PNG")
    img_str = base64.b64encode(buffered.getvalue()).decode()
    return img_str
